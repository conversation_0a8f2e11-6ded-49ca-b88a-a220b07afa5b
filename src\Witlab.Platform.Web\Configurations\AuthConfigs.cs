﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.IdentityModel.JsonWebTokens;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Text;
using Witlab.Platform.Core.Common.Interfaces;
using Witlab.Platform.Infrastructure.Auth;
using Witlab.Platform.Infrastructure.Auth.Interfaces;
using Witlab.Platform.Web.Configurations.Auth;
using Witlab.Platform.Web.Extensions;

namespace Witlab.Platform.Web.Configurations;

public static class AuthConfigs
{
  public static IServiceCollection AddAuthConfigs(this IServiceCollection services, Microsoft.Extensions.Logging.ILogger logger, WebApplicationBuilder builder)
  {
    services.AddScoped<IUser, CurrentUser>();
    services.AddSingleton(TimeProvider.System);

    // 配置JWT认证
    var jwtSettings = builder.Configuration.GetSection("JwtSettings").Get<JwtSettings>();
    if (jwtSettings != null)
    {
      services.AddAuthentication(options =>
      {
        options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
        options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
      })
      .AddJwtBearer(options =>
      {
        options.TokenValidationParameters = new TokenValidationParameters
        {
          ValidateIssuer = true,
          ValidateAudience = true,
          ValidateLifetime = true,
          ValidateIssuerSigningKey = true,
          ValidIssuer = jwtSettings.Issuer,
          ValidAudience = jwtSettings.Audience,
          IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSettings.Secret)),
          ClockSkew = TimeSpan.Zero
        };

        options.Events = new JwtBearerEvents
        {
          OnTokenValidated = async context =>
          {
            var jwtService = context.HttpContext.RequestServices.GetRequiredService<IJwtService>();
            var token = context.SecurityToken as JsonWebToken;
            if (token != null)
            {
              var isBlacklisted = await jwtService.IsInBlacklistAsync(token.EncodedToken);
              if (isBlacklisted)
              {
                context.Fail("Token has been revoked");
              }
            }
          }
        };
      });
    }

    // 添加授权服务
    services.AddAuthorization();
    services.AddSingleton<IAuthorizationPolicyProvider, PermissionPolicyProvider>();
    services.AddScoped<IAuthorizationHandler, PermissionAuthorizationHandler>();

    logger.LogInformation("Auth is configurated");

    return services;
  }

}
