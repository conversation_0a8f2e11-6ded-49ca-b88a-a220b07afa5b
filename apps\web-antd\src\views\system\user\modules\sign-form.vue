<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Card, CardMeta, Image, Space, Spin } from 'ant-design-vue';
import { Edit, Trash } from 'lucide-vue-next';

import { callServer, getUserInfoFromWitlab, UploadWitlabFile } from '#/api';
import { $t } from '#/locales';
import { isNullOrWhiteSpace } from '#/utils';

const emit = defineEmits(['success']);

function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.addEventListener('load', () => resolve(reader.result as string));
    reader.addEventListener('error', () => reject(reader.error));
  });
}

interface SignFormData {
  origrec: number;
  userName: string;
  realName: string;
  chsSignPic: {
    image: string;
    starDocId: string;
  };
  enSignPic: {
    image: string;
    starDocId: string;
  };
}

const formData = ref<SignFormData>();
const getTitle = ref($t('ui.actionTitle.view', [$t('ui.actionTitle.signPic')]));
const isChsSignLoading = ref(false);
const isEngSignLoading = ref(false);

const [Modal, modalApi] = useVbenModal({
  footer: false,
  closeOnClickModal: false,
  async onConfirm() {
    modalApi.lock();
    try {
      modalApi.close();
      emit('success');
    } finally {
      modalApi.lock(false);
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      isChsSignLoading.value = true;
      isEngSignLoading.value = true;
      const data = modalApi.getData<SignFormData>();
      const { origrec, starDocId, engStarDocId } = await getUserInfoFromWitlab(
        data.userName,
      );
      formData.value = {
        origrec,
        userName: data.userName,
        realName: data.realName,
        chsSignPic: {
          image: '',
          starDocId: '',
        },
        enSignPic: {
          image: '',
          starDocId: '',
        },
      };
      if (!isNullOrWhiteSpace(starDocId)) {
        const chsSignPic = await callServer(
          'RUNTIME_SUPPORT.GetImageFromSTARDOCBase64',
          [starDocId],
        );
        formData.value.chsSignPic = {
          image: chsSignPic,
          starDocId,
        };
      }
      if (!isNullOrWhiteSpace(engStarDocId)) {
        const enSignPic = await callServer(
          'RUNTIME_SUPPORT.GetImageFromSTARDOCBase64',
          [engStarDocId],
        );
        formData.value.enSignPic = {
          image: enSignPic,
          starDocId: engStarDocId,
        };
      }
      isChsSignLoading.value = false;
      isEngSignLoading.value = false;
    }
  },
});

async function handleUpdateSign(
  type: 'chs' | 'en',
  starDocId: string,
  newImageBase64?: string,
) {
  try {
    const fieldToUpdate = type === 'chs' ? 'STARDOC_ID' : 'ENGSTARDOC_ID';
    await callServer('Common.Update', [
      'USERS',
      fieldToUpdate,
      starDocId,
      formData.value?.origrec,
    ]);

    if (formData.value) {
      if (type === 'chs') {
        formData.value.chsSignPic = {
          image: newImageBase64 || '',
          starDocId,
        };
      } else {
        formData.value.enSignPic = {
          image: newImageBase64 || '',
          starDocId,
        };
      }
    }
  } catch (error) {
    console.error('Update failed', error);
  }
}

const handleEdit = async (type: 'chs' | 'en') => {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = 'image/*';
  input.addEventListener('change', async (e) => {
    const files = (e.target as HTMLInputElement).files;
    if (files && files.length > 0) {
      const file = files[0];
      if (file) {
        try {
          type === 'chs'
            ? (isChsSignLoading.value = true)
            : (isEngSignLoading.value = true);
          const newStarDocId = await UploadWitlabFile(file as File);
          if (newStarDocId) {
            const newImageBase64 = await fileToBase64(file);
            await handleUpdateSign(type, newStarDocId, newImageBase64);
          }
        } catch (error) {
          console.error('Upload failed', error);
        } finally {
          type === 'chs'
            ? (isChsSignLoading.value = false)
            : (isEngSignLoading.value = false);
        }
      }
    }
  });
  input.click();
};

const handleDelete = async (type: 'chs' | 'en') => {
  try {
    await callServer('UserManagement.deleteLogo', [
      formData.value?.userName,
      type === 'chs'
        ? formData.value?.chsSignPic.starDocId
        : formData.value?.enSignPic.starDocId,
    ]);
    await handleUpdateSign(type, '');
  } catch (error) {
    console.error('Delete failed', error);
  }
};
</script>

<template>
  <Modal :title="getTitle" class="w-[720px]">
    <Space class="flex justify-center gap-4">
      <Card hoverable class="m-4 min-h-[420px] min-w-[300px]">
        <template #cover>
          <Spin :spinning="isChsSignLoading">
            <Image
              :src="formData?.chsSignPic?.image"
              class="min-h-[300px] w-full"
            />
          </Spin>
        </template>
        <template #actions>
          <!-- <div class="flex w-full justify-center">
            <Settings key="setting" class="size-4" />
          </div> -->
          <div class="flex w-full justify-center">
            <Edit key="edit" class="size-4" @click="handleEdit('chs')" />
          </div>
          <div class="flex w-full justify-center">
            <Trash key="delete" class="size-4" @click="handleDelete('chs')" />
          </div>
          <!-- <div class="flex w-full justify-center">
            <Ellipsis key="ellipsis" class="size-4" />
          </div> -->
        </template>
        <CardMeta
          :title="$t('system.user.chsSignPic')"
          :description="formData?.realName"
        >
          <template #avatar>
            <a-avatar src="https://joeschmoe.io/api/v1/random" />
          </template>
        </CardMeta>
      </Card>
      <Card hoverable class="m-4 min-h-[420px] min-w-[300px]">
        <template #cover>
          <Spin :spinning="isEngSignLoading">
            <Image
              :src="formData?.enSignPic?.image"
              class="min-h-[300px] w-full"
            />
          </Spin>
        </template>
        <template #actions>
          <div class="flex w-full justify-center">
            <Edit key="edit" class="size-4" @click="handleEdit('en')" />
          </div>
          <div class="flex w-full justify-center">
            <Trash key="delete" class="size-4" @click="handleDelete('en')" />
          </div>
        </template>
        <CardMeta
          :title="$t('system.user.enSignPic')"
          :description="formData?.userName"
        >
          <template #avatar>
            <a-avatar src="https://joeschmoe.io/api/v1/random" />
          </template>
        </CardMeta>
      </Card>
    </Space>
  </Modal>
</template>
